document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a registration page
    const clientForm = document.getElementById('clientForm');
    const caregiverForm = document.getElementById('caregiverForm');

    if (clientForm || caregiverForm) {
        // Don't automatically load questions - let the registration forms handle their own language-specific loading
        // loadQuestions(); // REMOVED - now handled by inline JavaScript in registration forms

        const form = clientForm || caregiverForm;
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm(form, clientForm ? 'client' : 'caregiver');
        });
    }

    // Modern option selection is handled in createQuestionCard function
});

// Load questions from the API
function loadQuestions() {
    const questionsContainer = document.getElementById('questionsContainer');

    if (!questionsContainer) return;

    fetch('/questions')
        .then(response => response.json())
        .then(questions => {
            // Group questions by category
            const categories = groupByCategory(questions);

            // Clear loading indicator
            questionsContainer.innerHTML = '';

            // Create section for each category
            for (const [category, categoryQuestions] of Object.entries(categories)) {
                const categoryClass = getCategoryClass(category);

                const categorySection = document.createElement('div');
                categorySection.className = `form-section ${categoryClass}`;

                const categoryTitle = document.createElement('h5');
                categoryTitle.className = 'mb-3';
                categoryTitle.textContent = category;
                categorySection.appendChild(categoryTitle);

                // Add questions for this category
                categoryQuestions.forEach(question => {
                    const questionCard = createQuestionCard(question, categoryClass);
                    categorySection.appendChild(questionCard);
                });

                questionsContainer.appendChild(categorySection);
            }
        })
        .catch(error => {
            console.error('Error loading questions:', error);
            questionsContainer.innerHTML = `
                <div class="alert alert-danger">
                    Failed to load questions. Please try again later.
                </div>
            `;
        });
}

// Group questions by category
function groupByCategory(questions) {
    return questions.reduce((acc, question) => {
        const category = question.scoringCategory;
        if (!acc[category]) {
            acc[category] = [];
        }
        acc[category].push(question);
        return acc;
    }, {});
}

// Get CSS class for a category
function getCategoryClass(category) {
    const categoryMap = {
        'Personality & Communication Match': 'personality',
        'Care Needs & Skills Alignment': 'care-needs',
        'Lifestyle Interests Match': 'lifestyle',
        'Cultural/Language/Location Fit': 'cultural',
        'Logistics & Schedule Compatibility': 'logistics'
    };

    return categoryMap[category] || '';
}

// Create a question card element with modern design
function createQuestionCard(question, categoryClass) {
    // Create question card
    const questionCard = document.createElement('div');
    questionCard.className = 'question-card';
    questionCard.dataset.questionIndex = question.id;
    questionCard.dataset.questionText = question.questionText;

    // Create question header
    const questionHeader = document.createElement('div');
    questionHeader.className = 'question-header';

    const questionTitle = document.createElement('h5');
    questionTitle.className = 'question-title';
    questionTitle.textContent = question.questionText;
    questionHeader.appendChild(questionTitle);
    questionCard.appendChild(questionHeader);

    // Create question body
    const questionBody = document.createElement('div');
    questionBody.className = 'question-body';

    // Hidden field for question text
    const questionInput = document.createElement('input');
    questionInput.type = 'hidden';
    questionInput.name = `responses[][questionText]`;
    questionInput.value = question.questionText;
    questionBody.appendChild(questionInput);

    // Add options with modern card design
    question.options.forEach((option, index) => {
        const optionCard = document.createElement('div');
        optionCard.className = 'option-card';

        const input = document.createElement('input');
        input.className = 'form-check-input';
        input.type = 'checkbox';
        input.value = option;
        input.name = `responses[][selectedOptions][]`;
        input.id = `option-${question.id}-${index}`;

        const label = document.createElement('label');
        label.className = 'form-check-label';
        label.htmlFor = `option-${question.id}-${index}`;
        label.textContent = option;

        // Add elements to option card
        optionCard.appendChild(input);
        optionCard.appendChild(label);

        // Enhanced change event handler
        input.addEventListener('change', function(e) {
            console.log('Checkbox changed:', this.checked, this.value);

            // Update visual state based on checked status
            if (this.checked) {
                optionCard.classList.add('selected');
                console.log('Checkbox selected:', this.value);
            } else {
                optionCard.classList.remove('selected');
                console.log('Checkbox deselected:', this.value);
            }
        });

        // Also handle clicks on the entire card
        optionCard.addEventListener('click', function(e) {
            // Don't trigger if clicking directly on checkbox or label
            if (e.target === input || e.target === label) return;

            // Toggle the checkbox
            input.checked = !input.checked;
            input.dispatchEvent(new Event('change'));
        });

        console.log('Event listeners attached to:', input.id);

        questionBody.appendChild(optionCard);
    });

    questionCard.appendChild(questionBody);
    return questionCard;
}

// Submit the form
function submitForm(form, type) {
    console.log('submitForm called with:', { form: form.id, type });

    // Prevent duplicate submissions
    if (form.dataset.submitting === 'true') {
        console.log('Form submission already in progress, ignoring duplicate');
        return;
    }

    // Mark form as submitting
    form.dataset.submitting = 'true';

    // Debug form structure
    console.log('Form HTML:', form.outerHTML.substring(0, 500));
    console.log('Form inputs:', form.querySelectorAll('input').length);
    console.log('Question cards:', form.querySelectorAll('.question-card').length);

    // Validate form first
    if (!validateForm(form)) {
        form.dataset.submitting = 'false'; // Reset on validation failure
        return;
    }

    // Set loading state
    setLoadingState(form, true);

    // Get name directly from the input field
    const nameInput = form.querySelector('input[name="name"]');
    const name = nameInput ? nameInput.value.trim() : '';

    // Get language from the select field
    const languageSelect = form.querySelector('select[name="language"]');
    const language = languageSelect ? languageSelect.value : 'en';

    console.log('Name input found:', !!nameInput);
    console.log('Name value:', name);
    console.log('Language select found:', !!languageSelect);
    console.log('Language value:', language);

    const data = {
        name: name,
        language: language,
        responses: []
    };

    console.log('Form data:', data);

    // Get all questions using the new structure
    const questionCards = form.querySelectorAll('.question-card');
    questionCards.forEach(card => {
        const questionText = card.dataset.questionText;

        // Use browser's native :checked selector
        const selectedOptions = Array.from(
            card.querySelectorAll('input[type="checkbox"]:checked')
        ).map(input => input.value);

        console.log(`Question: ${questionText}, Selected: ${selectedOptions.length} options:`, selectedOptions);

        if (selectedOptions.length > 0) {
            data.responses.push({
                questionText,
                selectedOptions
            });
        }
    });

    // Submit the data
    console.log('Submitting to:', `/${type === 'client' ? 'clients' : 'caregivers'}`);
    console.log('Payload:', JSON.stringify(data, null, 2));

    fetch(`/${type === 'client' ? 'clients' : 'caregivers'}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (response.ok) {
            return response.json();
        }

        // Try to get error details
        return response.text().then(text => {
            console.log('Error response body:', text);
            throw new Error(`Submission failed: ${response.status} - ${text}`);
        });
    })
    .then(result => {
        // Show success message
        showNotification('Registration successful!', 'success');

        // Don't reset submitting state since we're redirecting
        // Redirect to profile page
        setTimeout(() => {
            window.location.href = `/${type}/${result.id}`;
        }, 1500);
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        showNotification('Registration failed. Please try again.', 'error');

        // Reset submission state and loading state on error
        form.dataset.submitting = 'false';
        setLoadingState(form, false);
    });
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Enhanced form validation
function validateForm(form) {
    console.log('Validating form...');

    const nameInput = form.querySelector('input[name="name"]');
    console.log('Name input found:', !!nameInput);

    if (!nameInput) {
        console.error('Name input not found in form');
        showNotification('Form error: Name field not found', 'error');
        return false;
    }

    const name = nameInput.value.trim();
    console.log('Name value:', name);

    if (!name) {
        showNotification('Please enter your name', 'error');
        return false;
    }

    // Validate language selection
    const languageSelect = form.querySelector('select[name="language"]');
    if (!languageSelect || !languageSelect.value) {
        showNotification('Please select your preferred language', 'error');
        return false;
    }

    // Check if at least one question has been answered
    const questionCards = form.querySelectorAll('.question-card');
    let hasAnswers = false;

    questionCards.forEach(card => {
        const checkboxes = card.querySelectorAll('input[type="checkbox"]:checked');
        if (checkboxes.length > 0) {
            hasAnswers = true;
        }
    });

    if (!hasAnswers) {
        showNotification('Please answer at least one question', 'error');
        return false;
    }

    return true;
}

// Smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Loading state management
function setLoadingState(form, isLoading) {
    const submitButton = form.querySelector('button[type="submit"]');
    const inputs = form.querySelectorAll('input, button');

    if (isLoading) {
        submitButton.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            Submitting...
        `;
        inputs.forEach(input => input.disabled = true);
    } else {
        submitButton.innerHTML = submitButton.dataset.originalText || 'Submit Registration';
        inputs.forEach(input => input.disabled = false);
    }
}

// Modern option selection and ripple effects are handled in createQuestionCard function
