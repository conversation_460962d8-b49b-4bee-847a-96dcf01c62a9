import Fluent
import Vapor

struct LanguageController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        let languages = routes.grouped("api", "languages")
        
        // Get all available languages
        languages.get(use: getLanguages)
        
        // Get questions in specific language
        languages.get(":languageCode", "questions", use: getQuestionsInLanguage)
        
        // Set language preference (could be stored in session/cookie)
        languages.post("set", ":languageCode", use: setLanguage)
    }
    
    // GET /api/languages
    @Sendable
    func getLanguages(req: Request) async throws -> [LanguageResponse] {
        let languages = try await Language.query(on: req.db).all()
        return languages.map { language in
            LanguageResponse(
                code: language.code,
                name: language.name,
                isDefault: language.isDefault
            )
        }
    }
    
    // GET /api/languages/:languageCode/questions
    @Sendable
    func getQuestionsInLanguage(req: Request) async throws -> [LocalizedQuestion] {
        guard let languageCode = req.parameters.get("languageCode") else {
            throw Abort(.badRequest, reason: "Language code is required")
        }
        
        // Validate language exists
        guard let _ = try await Language.query(on: req.db)
            .filter(\.$code == languageCode)
            .first() else {
            throw Abort(.notFound, reason: "Language not found")
        }
        
        let questions = try await Question.query(on: req.db).all()
        var localizedQuestions: [LocalizedQuestion] = []
        
        for question in questions {
            let (text, options) = try await question.getLocalizedContent(for: languageCode, on: req.db)
            
            let localizedQuestion = LocalizedQuestion(
                id: question.id,
                questionText: text,
                scoringCategory: question.scoringCategory,
                options: options,
                language: languageCode
            )
            localizedQuestions.append(localizedQuestion)
        }
        
        return localizedQuestions
    }
    
    // POST /api/languages/set/:languageCode
    @Sendable
    func setLanguage(req: Request) async throws -> HTTPStatus {
        guard let languageCode = req.parameters.get("languageCode") else {
            throw Abort(.badRequest, reason: "Language code is required")
        }
        
        // Validate language exists
        guard let _ = try await Language.query(on: req.db)
            .filter(\.$code == languageCode)
            .first() else {
            throw Abort(.notFound, reason: "Language not found")
        }
        
        // Store language preference in session
        req.session.data["language"] = languageCode
        
        return .ok
    }
}

// Extension to get current language from session
extension Request {
    var currentLanguage: String {
        return session.data["language"] ?? "en"
    }
}
