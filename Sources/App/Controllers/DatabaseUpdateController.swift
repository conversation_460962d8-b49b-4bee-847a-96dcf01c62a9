import Fluent
import Vapor

struct DatabaseUpdateController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        let admin = routes.grouped("admin", "database")
        
        // Update certification options
        admin.post("update-certifications", use: updateCertifications)
        
        // Create language tables and data
        admin.post("setup-languages", use: setupLanguages)
    }
    
    // POST /admin/database/update-certifications
    @Sendable
    func updateCertifications(req: Request) async throws -> HTTPStatus {
        // Update the Certifications question to replace old options with RN
        if let certificationQuestion = try await Question.query(on: req.db)
            .filter(\.$questionText == "Certifications")
            .first() {
            
            // Update the options array
            certificationQuestion.options = ["CNA", "HHA", "RN"]
            try await certificationQuestion.save(on: req.db)
        }
        
        return .ok
    }
    
    // POST /admin/database/setup-languages
    @Sendable
    func setupLanguages(req: Request) async throws -> HTTPStatus {
        // For now, let's just update the certification options and return success
        // The language feature will be implemented in a future update

        // Update the Certifications question to replace old options with RN
        if let certificationQuestion = try await Question.query(on: req.db)
            .filter(\.$questionText == "Certifications")
            .first() {

            // Update the options array
            certificationQuestion.options = ["CNA", "HHA", "RN"]
            try await certificationQuestion.save(on: req.db)
        }

        return .ok
    }
    
    private func getSpanishTranslation(for questionText: String, options: [String]) -> (String, [String]) {
        let translations: [String: (text: String, options: [String])] = [
            "Preferred personality type": (
                text: "Tipo de personalidad preferido",
                options: ["Tranquilo y silencioso", "Extrovertido y hablador", "Equilibrado y adaptable", "Enérgico y entusiasta", "Paciente y reservado"]
            ),
            "Preferred communication style": (
                text: "Estilo de comunicación preferido",
                options: ["Instrucciones directas y claras", "Casual y conversacional", "Con paciencia y explicaciones detalladas", "Usando ayudas visuales o demostraciones"]
            ),
            "Preferred care type": (
                text: "Tipo de cuidado preferido",
                options: ["Cuidado personal", "Compañía", "Manejo de medicamentos", "Limpieza ligera", "Preparación de comidas", "Transporte"]
            ),
            "Preferred schedule": (
                text: "Horario preferido",
                options: ["Mañana (6 AM - 12 PM)", "Tarde (12 PM - 6 PM)", "Noche (6 PM - 12 AM)", "Overnight (12 AM - 6 AM)", "Flexible"]
            ),
            "Certifications": (
                text: "Certificaciones",
                options: ["CNA", "HHA", "RN"]
            ),
            "Medical comfort level": (
                text: "Nivel de comodidad médica",
                options: ["Transferencias de elevación/movilidad", "Deterioro cognitivo/cuidado de la memoria", "Equipo médico (tubos, colostomía)", "Compañía emocional"]
            ),
            "Preferred activities": (
                text: "Actividades preferidas",
                options: ["Lectura", "Música", "Ejercicio ligero", "Juegos", "Artesanías", "Jardinería", "Cocinar", "Ver televisión"]
            ),
            "Preferred location": (
                text: "Ubicación preferida",
                options: ["Centro de la ciudad", "Suburbios", "Lado norte", "Lado sur", "Lado este", "Lado oeste"]
            ),
            "Language preference": (
                text: "Preferencia de idioma",
                options: ["Inglés", "Español", "Francés", "Mandarín", "Alemán", "Italiano"]
            ),
            "Cultural background": (
                text: "Antecedentes culturales",
                options: ["Americano", "Hispano/Latino", "Asiático", "Afroamericano", "Europeo", "Medio Oriente", "Nativo Americano", "Mixto", "Otro"]
            ),
            "Preferred gender": (
                text: "Género preferido",
                options: ["Masculino", "Femenino", "Sin preferencia"]
            ),
            "Experience level": (
                text: "Nivel de experiencia",
                options: ["0-1 años", "1-3 años", "3-5 años", "5-10 años", "10+ años"]
            ),
            "Availability": (
                text: "Disponibilidad",
                options: ["Tiempo completo", "Medio tiempo", "Según sea necesario", "Fines de semana solamente", "Días de semana solamente"]
            ),
            "Transportation": (
                text: "Transporte",
                options: ["Tengo vehículo propio", "Transporte público", "Necesito transporte", "Puedo caminar/bicicleta"]
            ),
            "Rate preference": (
                text: "Preferencia de tarifa",
                options: ["$15-20/hora", "$20-25/hora", "$25-30/hora", "$30-35/hora", "$35+/hora"]
            )
        ]
        
        if let translation = translations[questionText] {
            return (translation.text, translation.options)
        }
        
        // Fallback to English if no translation found
        return (questionText, options)
    }
}

struct LanguageID: Content {
    let id: UUID
}
