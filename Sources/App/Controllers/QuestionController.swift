//
//  QuestionsController.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/21/25.
//

import Fluent
import Vapor

struct QuestionController: RouteCollection {
    
    func boot(routes: any RoutesBuilder) throws {
        let questions = routes.grouped("questions")
        
        // Get all questions
        questions.get(use: index)
        
        // Get questions by category
        questions.get("category", ":category", use: getByCategory)
        
        // Get all answer options
        questions.get("options", use: getAnswerOptions)
    }
    
    // GET /questions
    @Sendable
    func index(req: Request) async throws ->[Question] {
        try await Question.query(on: req.db).all()
    }
    
    // GET /questions/category/:category
    @Sendable
    func getByCategory(req: Request) async throws -> [Question] {
        guard let category = req.parameters.get("category") else {
            throw Abort(.badRequest, reason: "Category parameter is required")
        }
        
        return try await Question.query(on: req.db)
            .filter(\.$scoringCategory == category)
            .all()
    }
    
    // GET /questions/options
    @Sendable
    func getAnswerOptions(req: Request) async throws -> [AnswerOption] {
        try await AnswerOption.query(on: req.db).all()
    }
}
