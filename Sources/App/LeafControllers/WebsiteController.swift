//
//  WebsiteController.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/25/25.
//

import Vapor
import Fluent


struct AllClientsContext: Encodable {
    let title: String
    let clients: [Client]
}
struct AllCaregiversContext: Encodable {
    let title: String
    let caregivers: [Caregiver]
}

struct WebsiteController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        // Base routes
        routes.get(use: indexHandler)

        // Client routes (forwarded to ClientWebController)
        let clientRoutes = routes.grouped("client")
        let clientController = ClientWebController()
        try clientRoutes.register(collection: clientController)

        // Caregiver routes (forwarded to CaregiverWebController)
        let caregiverRoutes = routes.grouped("caregiver")
        let caregiverController = CaregiverWebController()
        try caregiverRoutes.register(collection: caregiverController)

        // Routes to list all clients/caregivers
        routes.get("clients", use: allClientsHandler)
        routes.get("caregivers", use: allCaregiversHandler)

        // Test route for debugging JavaScript
        routes.get("test", use: testHandler)

        // Language test route
        routes.get("language-test", use: languageTestHandler)
    }

    // GET /
    func indexHandler(_ req: Request) async throws -> View {
        return try await req.view.render("index", ["title": "Home"])
    }

    // GET /clients
    func allClientsHandler(_ req: Request) async throws -> View {
        let clients = try await Client.query(on: req.db).all()
        let context = AllClientsContext(title: "All Clients", clients: clients)
        return try await req.view.render("client/list", context)
    }

    //js
//    func allClientsHandler(_ req: Request) async throws -> [Client] {
//        return  try await Client.query(on: req.db).all()
//    }

    // GET /caregivers
    func allCaregiversHandler(_ req: Request) async throws -> View {
        let caregivers = try await Caregiver.query(on: req.db).all()
        let context = AllCaregiversContext(title: "All Caregivers", caregivers: caregivers)
        return try await req.view.render("caregiver/list", context)
    }

    //js
//    func allCaregiversHandler(_ req: Request) async throws -> [Caregiver] {
//        return try await Caregiver.query(on: req.db).all()
////        let context = AllCaregiversContext(title: "All Caregivers", caregivers: caregivers)
////        return try await req.view.render("caregiver/list", context)
//    }

    // GET /test
    func testHandler(_ req: Request) async throws -> View {
        return try await req.view.render("test", ["title": "JavaScript Test"])
    }

    // GET /language-test
    func languageTestHandler(_ req: Request) async throws -> View {
        return try await req.view.render("language-test", ["title": "Language Test"])
    }
}
