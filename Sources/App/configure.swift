import NIOSSL
import Fluent
import FluentPostgresDriver
import Vapor
import Leaf

// configures your application
public func configure(_ app: Application) async throws {
    // Custom middleware to prevent caching of CSS and JS files
    app.middleware.use(NoCacheMiddleware())

    // uncomment to serve files from /Public folder
    app.middleware.use(FileMiddleware(publicDirectory: app.directory.publicDirectory))

    // Configure Leaf for templating
    let corsConfiguration = CORSMiddleware.Configuration(
        allowedOrigin: .all,
        allowedMethods: [.GET, .POST, .PUT, .OPTIONS, .DELETE, .PATCH],
        allowedHeaders: [.accept, .authorization, .contentType, .origin, .xRequestedWith, .userAgent, .accessControlAllowOrigin]
    )
    let cors = CORSMiddleware(configuration: corsConfiguration)
    app.middleware.use(cors, at: .beginning)

    // Configure sessions for language preferences
    app.middleware.use(app.sessions.middleware)

    // Configure database - support both Heroku DATABASE_URL and individual env vars
    if let databaseURL = Environment.get("DATABASE_URL") {
        try app.databases.use(.postgres(url: databaseURL), as: .psql)
    } else {
        app.databases.use(DatabaseConfigurationFactory.postgres(configuration: .init(
            hostname: Environment.get("DATABASE_HOST") ?? "localhost",
            port: Environment.get("DATABASE_PORT").flatMap(Int.init(_:)) ?? SQLPostgresConfiguration.ianaPortNumber,
            username: Environment.get("DATABASE_USERNAME") ?? "kylecarriedo",
            password: Environment.get("DATABASE_PASSWORD") ?? "#Sobvalen06",
            database: Environment.get("DATABASE_NAME") ?? "matchiq",
            tls: .prefer(try .init(configuration: .clientDefault)))
        ), as: .psql)
    }

    // Add migrations for production deployment
    app.migrations.add(CreateClient())
    app.migrations.add(CreateCaregiver())
    app.migrations.add(CreateQuestion())
    app.migrations.add(CreateAnswerOption())
    app.migrations.add(CreateClientResponse())
    app.migrations.add(CreateCaregiverResponse())
    app.migrations.add(CreateMatchResult())
    app.migrations.add(CreateMatchConfirmation())
    app.migrations.add(SeedQuestionAndAnswerOptions())

    // New migrations for certification updates and language support
    app.migrations.add(UpdateCertificationOptions())
    app.migrations.add(CreateLanguageSupport())
    app.migrations.add(AddLanguageToClientsAndCaregivers())








    app.views.use(.leaf)

    // register routes
    try routes(app)
}
