#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-4">
                    <div class="d-inline-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle p-3 mb-3">
                        <i class="fas fa-user-friends text-primary fs-2"></i>
                    </div>
                    <h1 class="fw-bold mb-2">Client Registration</h1>
                    <p class="lead text-muted">Find your perfect caregiver match</p>
                </div>

                <div class="card shadow-lg border-0">
                    <div class="card-body p-4 p-md-5">
                        <form id="clientForm">
                            <div class="mb-4">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-2 text-primary"></i>
                                    Full Name
                                </label>
                                <input type="text" class="form-control form-control-lg" id="name" name="name" required
                                       placeholder="Enter your full name">
                            </div>

                            <div class="mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-clipboard-list text-primary me-2"></i>
                                    <h4 class="mb-0 fw-semibold">Care Preferences Questionnaire</h4>
                                </div>
                                <p class="text-muted mb-4">Please answer the following questions to help us find the best caregiver match for you. Your responses help us understand your care needs and preferences.</p>

                                <div id="questionsContainer" class="mb-4">
                                    <!-- Questions will be loaded here via JavaScript -->
                                    <div class="text-center py-5">
                                        <div class="spinner-border text-primary mb-3" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="text-muted">Loading questionnaire...</p>
                                        <small class="text-muted">This may take a few moments</small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" data-original-text="Submit Registration">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Submit Registration
                                </button>
                                <small class="text-muted text-center mt-2">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Your information is secure and confidential
                                </small>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <p class="text-muted">
                        Already registered?
                        <a href="/clients" class="text-decoration-none">View all clients</a>
                    </p>
                </div>
            </div>
        </div>

        <style>
        /* Modern Question Card Design */
        .question-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid #f0f0f0;
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .question-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            margin: 0;
        }

        .question-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.4;
        }

        .question-body {
            padding: 1.5rem;
        }

        /* Modern Option Cards */
        .option-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            margin-bottom: 0.75rem;
            padding: 1rem 1.25rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            min-height: 60px;
        }

        .option-card:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateX(4px);
        }

        .option-card.selected {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-color: #2196f3;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
        }

        .option-card input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin-right: 1rem;
            accent-color: #2196f3;
            cursor: pointer;
        }

        .option-card label {
            font-weight: 500;
            color: #333;
            cursor: pointer;
            flex: 1;
            margin: 0;
            line-height: 1.5;
        }

        .option-card.selected label {
            color: #1976d2;
            font-weight: 600;
        }

        /* Selection indicator */
        .option-card::after {
            content: '';
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: transparent;
            border: 2px solid #ddd;
            transition: all 0.3s ease;
        }

        .option-card.selected::after {
            background: #2196f3;
            border-color: #2196f3;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
            background-size: 12px;
            background-position: center;
            background-repeat: no-repeat;
        }

        /* Mobile Responsive Design */
        @media (max-width: 768px) {
            .question-header {
                padding: 1rem 1.25rem;
            }

            .question-title {
                font-size: 1rem;
            }

            .question-body {
                padding: 1.25rem;
            }

            .option-card {
                padding: 0.875rem 1rem;
                min-height: 50px;
            }

            .option-card label {
                font-size: 0.9rem;
            }

            .option-card::after {
                width: 20px;
                height: 20px;
                right: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .question-card {
                margin-bottom: 1.5rem;
                border-radius: 12px;
            }

            .question-header {
                padding: 0.875rem 1rem;
            }

            .question-body {
                padding: 1rem;
            }

            .option-card {
                padding: 0.75rem;
                margin-bottom: 0.5rem;
                border-radius: 8px;
            }

            .option-card:hover {
                transform: none;
            }
        }

        /* Legacy support for existing classes */
        .form-check {
            margin: 0;
            padding: 0;
        }

        .form-check-input {
            cursor: pointer;
            pointer-events: auto;
        }

        .form-check-label {
            cursor: pointer;
            pointer-events: auto;
        }

        .question-block {
            pointer-events: auto;
        }
        </style>

        <script>
        console.log('Script loaded');

        function loadQuestions() {
            console.log('Loading questions...');
            const questionsContainer = document.getElementById('questionsContainer');

            if (!questionsContainer) {
                console.error('Questions container not found');
                return;
            }

            fetch('/questions')
                .then(response => {
                    console.log('Response received:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(questions => {
                    console.log('Questions loaded:', questions.length);

                    // Clear loading indicator
                    questionsContainer.innerHTML = '';

                    // Add questions to form with modern card design
                    questions.forEach((question, index) => {
                        // Create question card
                        const questionCard = document.createElement('div');
                        questionCard.className = 'question-card';
                        questionCard.dataset.questionIndex = index;
                        questionCard.dataset.questionText = question.questionText;

                        // Create question header
                        const questionHeader = document.createElement('div');
                        questionHeader.className = 'question-header';

                        const questionTitle = document.createElement('h5');
                        questionTitle.className = 'question-title';
                        questionTitle.textContent = question.questionText;
                        questionHeader.appendChild(questionTitle);
                        questionCard.appendChild(questionHeader);

                        // Create question body
                        const questionBody = document.createElement('div');
                        questionBody.className = 'question-body';

                        // Hidden field for question text
                        const questionInput = document.createElement('input');
                        questionInput.type = 'hidden';
                        questionInput.name = `responses[][questionText]`;
                        questionInput.value = question.questionText;
                        questionBody.appendChild(questionInput);

                        // Add options with modern card design
                        question.options.forEach((option, optionIndex) => {
                            const optionCard = document.createElement('div');
                            optionCard.className = 'option-card';

                            const input = document.createElement('input');
                            input.className = 'form-check-input';
                            input.type = 'checkbox';
                            input.value = option;
                            input.name = `responses[][selectedOptions][]`;
                            input.id = `option-${index}-${optionIndex}`;

                            const label = document.createElement('label');
                            label.className = 'form-check-label';
                            label.htmlFor = `option-${index}-${optionIndex}`;
                            label.textContent = option;

                            // Add elements to option card
                            optionCard.appendChild(input);
                            optionCard.appendChild(label);

                            // Enhanced change event handler
                            input.addEventListener('change', function(e) {
                                console.log('Checkbox changed:', this.checked, this.value);

                                // Update visual state based on checked status
                                if (this.checked) {
                                    optionCard.classList.add('selected');
                                    console.log('Checkbox selected:', this.value);
                                } else {
                                    optionCard.classList.remove('selected');
                                    console.log('Checkbox deselected:', this.value);
                                }
                            });

                            // Also handle clicks on the entire card
                            optionCard.addEventListener('click', function(e) {
                                // Don't trigger if clicking directly on checkbox or label
                                if (e.target === input || e.target === label) return;

                                // Toggle the checkbox
                                input.checked = !input.checked;
                                input.dispatchEvent(new Event('change'));
                            });

                            console.log('Event listeners attached to:', input.id);

                            questionBody.appendChild(optionCard);
                        });

                        questionCard.appendChild(questionBody);
                        questionsContainer.appendChild(questionCard);
                    });
                })
                .catch(error => {
                    console.error('Error loading questions:', error);
                    questionsContainer.innerHTML = '<div class="alert alert-danger">Failed to load questions: ' + error.message + '</div>';
                });
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadQuestions);
        } else {
            loadQuestions();
        }

        // Form submission is handled by external script.js
        </script>

    #endexport

    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend
