#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>All Clients</h1>
            <a href="/client/register" class="btn btn-primary">Register New Client</a>
        </div>
        
        #if(clients):
            <div class="card shadow">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                #for(client in clients):
                                    <tr>
                                        <td>#(client.name)</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="/client/#(client.id)" class="btn btn-outline-primary">View Profile</a>
                                                <a href="/client/#(client.id)/matches" class="btn btn-outline-success">View Matches</a>
                                            </div>
                                        </td>
                                    </tr>
                                #endfor
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        #else:
            <div class="alert alert-info">No clients registered yet.</div>
        #endif
    #endexport
    
    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend
