#extend("base"):
#export("content"):
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary mb-3">
                        <i class="fas fa-language me-3"></i>Language Test
                    </h1>
                    <p class="lead text-muted">Test the Spanish translation functionality</p>
                </div>

                <!-- Language Selector -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Language Selection</h5>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="setLanguageAndReload('en')">
                                <i class="flag-icon flag-icon-us me-2"></i>English
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="setLanguageAndReload('es')">
                                <i class="flag-icon flag-icon-es me-2"></i>Español
                            </button>
                        </div>
                        <p class="text-muted mt-2 mb-0">Current language: <span id="currentLang">English</span></p>
                    </div>
                </div>

                <!-- Questions Display -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Sample Questions</h5>
                    </div>
                    <div class="card-body">
                        <div id="questionsContainer">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading questions...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'en';

        async function setLanguageAndReload(languageCode) {
            try {
                // Set up translations first
                await fetch('/admin/database/setup-languages', {
                    method: 'POST'
                });

                // Set language preference
                await fetch(`/api/languages/set/${languageCode}`, {
                    method: 'POST'
                });

                currentLanguage = languageCode;
                document.getElementById('currentLang').textContent = languageCode === 'en' ? 'English' : 'Español';
                
                // Load questions in the selected language
                await loadQuestions(languageCode);
                
            } catch (error) {
                console.error('Error switching language:', error);
                alert('Error switching language: ' + error.message);
            }
        }

        async function loadQuestions(languageCode) {
            try {
                const response = await fetch(`/api/languages/${languageCode}/questions`);
                const questions = await response.json();
                
                const container = document.getElementById('questionsContainer');
                container.innerHTML = '';
                
                // Show first 5 questions as examples
                const sampleQuestions = questions.slice(0, 5);
                
                sampleQuestions.forEach((question, index) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = 'mb-4 p-3 border rounded';
                    questionDiv.innerHTML = `
                        <h6 class="text-primary">${index + 1}. ${question.questionText}</h6>
                        <div class="mt-2">
                            ${question.options.map(option => `
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="question${index}" value="${option}">
                                    <label class="form-check-label">${option}</label>
                                </div>
                            `).join('')}
                        </div>
                        <small class="text-muted">Category: ${question.scoringCategory}</small>
                    `;
                    container.appendChild(questionDiv);
                });
                
            } catch (error) {
                console.error('Error loading questions:', error);
                document.getElementById('questionsContainer').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading questions: ${error.message}
                    </div>
                `;
            }
        }

        // Load English questions on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadQuestions('en');
        });
    </script>

    <style>
        .flag-icon {
            width: 16px;
            height: 12px;
            background-size: cover;
            display: inline-block;
        }

        .flag-icon-us {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxNiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiBmaWxsPSIjQjIyMjM0Ii8+CjxyZWN0IHk9IjEiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSIzIiB3aWR0aD0iMTYiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjE2IiBoZWlnaHQ9IjEiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHk9IjciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSI5IiB3aWR0aD0iMTYiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPHJlY3QgeT0iMTEiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSI3IiBmaWxsPSIjM0MzQjZFIi8+Cjwvc3ZnPgo=');
        }

        .flag-icon-es {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxNiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiBmaWxsPSIjRkZEQTAwIi8+CjxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIzIiBmaWxsPSIjREEwMjBFIi8+CjxyZWN0IHk9IjkiIHdpZHRoPSIxNiIgaGVpZ2h0PSIzIiBmaWxsPSIjREEwMjBFIi8+Cjwvc3ZnPgo=');
        }
    </style>
#endexport
#endextend
