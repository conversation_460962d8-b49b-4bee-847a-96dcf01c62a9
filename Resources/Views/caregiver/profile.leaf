#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Caregiver Profile</h1>
            <div class="btn-group">
                <a href="/caregiver/#(caregiver.id)/matches" class="btn btn-primary">View Client Matches</a>
                <button type="button" class="btn btn-danger" onclick="deleteCaregiver('#(caregiver.id)')">
                    <i class="fas fa-trash me-2"></i>Delete Profile
                </button>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-body">
                <h2 class="card-title mb-4">#(caregiver.name)</h2>

                <h4 class="mb-3">Your Responses</h4>

                #if(responses):
                    #for(response in responses):
                        <div class="card mb-3">
                            <div class="card-header">
                                <strong>#(response.questionText)</strong>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    #for(option in response.selectedOptions):
                                        <li class="list-group-item">#(option)</li>
                                    #endfor
                                </ul>
                            </div>
                        </div>
                    #endfor
                #else:
                    <div class="alert alert-info">No responses found.</div>
                #endif
            </div>
        </div>
    #endexport

    #export("footer"):
        #extend("shared/footer")

        <script>
        function deleteCaregiver(caregiverId) {
            if (confirm('Are you sure you want to delete this caregiver profile? This action cannot be undone.')) {
                fetch(`/caregivers/${caregiverId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        alert('Caregiver profile deleted successfully');
                        window.location.href = '/caregivers';
                    } else {
                        throw new Error('Failed to delete caregiver');
                    }
                })
                .catch(error => {
                    console.error('Error deleting caregiver:', error);
                    alert('Failed to delete caregiver profile. Please try again.');
                });
            }
        }
        </script>
    #endexport
#endextend
