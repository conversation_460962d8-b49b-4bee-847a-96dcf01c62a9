#extend("base"):
#export("content"):
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary mb-3">
                        <i class="fas fa-database me-3"></i>Database Seeding
                    </h1>
                    <p class="lead text-muted">Manage your MatchIQ database with sample data</p>
                </div>

                <!-- Current Status Card -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Current Database Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="stat-item">
                                    <h3 class="text-primary">#(status.clients)</h3>
                                    <small class="text-muted">Clients</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stat-item">
                                    <h3 class="text-success">#(status.caregivers)</h3>
                                    <small class="text-muted">Caregivers</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stat-item">
                                    <h3 class="text-info">#(status.questions)</h3>
                                    <small class="text-muted">Questions</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-warning">#(status.clientResponses)</h3>
                                    <small class="text-muted">Client Responses</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-secondary">#(status.caregiverResponses)</h3>
                                    <small class="text-muted">Caregiver Responses</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Action Cards -->
                    <div class="row">
                        <!-- Seed All -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-seedling fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">Seed All Data</h5>
                                    <p class="card-text text-muted">
                                        Add 30 sample clients and 30 sample caregivers with realistic responses
                                    </p>
                                    <button class="btn btn-success btn-lg" onclick="seedData('all')">
                                        <i class="fas fa-plus me-2"></i>Seed All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Reset & Seed -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-redo fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">Reset & Seed</h5>
                                    <p class="card-text text-muted">
                                        Clear all existing data and create fresh sample data
                                    </p>
                                    <button class="btn btn-primary btn-lg" onclick="resetAndSeed()">
                                        <i class="fas fa-refresh me-2"></i>Reset & Seed
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Seed Clients Only -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user fa-3x text-info"></i>
                                    </div>
                                    <h5 class="card-title">Seed Clients Only</h5>
                                    <p class="card-text text-muted">
                                        Add 30 sample clients with questionnaire responses
                                    </p>
                                    <button class="btn btn-info btn-lg" onclick="seedData('clients')">
                                        <i class="fas fa-user-plus me-2"></i>Seed Clients
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Seed Caregivers Only -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user-nurse fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">Seed Caregivers Only</h5>
                                    <p class="card-text text-muted">
                                        Add 30 sample caregivers with questionnaire responses
                                    </p>
                                    <button class="btn btn-warning btn-lg" onclick="seedData('caregivers')">
                                        <i class="fas fa-user-plus me-2"></i>Seed Caregivers
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Danger Zone -->
                    <div class="card border-danger mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                <strong>Warning:</strong> This action will permanently delete all clients, caregivers, and their responses.
                            </p>
                            <button class="btn btn-outline-danger" onclick="clearAllData()">
                                <i class="fas fa-trash me-2"></i>Clear All Data
                            </button>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="text-center">
                        <a href="/clients" class="btn btn-outline-primary me-2">
                            <i class="fas fa-users me-2"></i>View Clients
                        </a>
                        <a href="/caregivers" class="btn btn-outline-success me-2">
                            <i class="fas fa-user-nurse me-2"></i>View Caregivers
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 id="loadingText">Processing...</h5>
                    <p class="text-muted mb-0">This may take a few moments</p>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stat-item {
            padding: 1rem;
        }
        .stat-item h3 {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
    </style>

    <script>
        async function seedData(type) {
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');

            loadingText.textContent = `Seeding ${type}...`;
            loadingModal.show();

            try {
                const response = await fetch(`/api/seed/${type}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                loadingModal.hide();

                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification(result.message || 'Seeding failed', 'error');
                }
            } catch (error) {
                loadingModal.hide();
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function resetAndSeed() {
            if (!confirm('This will delete ALL existing data and create new sample data. Are you sure?')) {
                return;
            }

            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');

            loadingText.textContent = 'Resetting and seeding database...';
            loadingModal.show();

            try {
                const response = await fetch('/api/seed/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                loadingModal.hide();

                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification(result.message || 'Reset failed', 'error');
                }
            } catch (error) {
                loadingModal.hide();
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function clearAllData() {
            if (!confirm('This will permanently delete ALL clients, caregivers, and responses. This action cannot be undone. Are you sure?')) {
                return;
            }

            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');

            loadingText.textContent = 'Clearing all data...';
            loadingModal.show();

            try {
                const response = await fetch('/api/seed/clear', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                loadingModal.hide();

                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification(result.message || 'Clear failed', 'error');
                }
            } catch (error) {
                loadingModal.hide();
                showNotification('Error: ' + error.message, 'error');
            }
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
#endexport
#endextend
